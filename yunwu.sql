/*
 Navicat Premium Dump SQL

 Source Server         : 调试
 Source Server Type    : MySQL
 Source Server Version : 50744 (5.7.44-log)
 Source Host           : ***************:3306
 Source Schema         : yunwu

 Target Server Type    : MySQL
 Target Server Version : 50744 (5.7.44-log)
 File Encoding         : 65001

 Date: 25/05/2025 08:55:28
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for Sy_Articlelog
-- ----------------------------
DROP TABLE IF EXISTS `Sy_Articlelog`;
CREATE TABLE `Sy_Articlelog`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `uid` varchar(240) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `time` date NOT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for Sy_Commentslog
-- ----------------------------
DROP TABLE IF EXISTS `Sy_Commentslog`;
CREATE TABLE `Sy_Commentslog`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `uid` varchar(240) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `time` date NOT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 27 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for Sy_Dynamiclog
-- ----------------------------
DROP TABLE IF EXISTS `Sy_Dynamiclog`;
CREATE TABLE `Sy_Dynamiclog`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `uid` varchar(240) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `time` date NOT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 2 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for Sy_Followlog
-- ----------------------------
DROP TABLE IF EXISTS `Sy_Followlog`;
CREATE TABLE `Sy_Followlog`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `uid` varchar(240) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `time` date NOT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 4 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for Sy_Forumlog
-- ----------------------------
DROP TABLE IF EXISTS `Sy_Forumlog`;
CREATE TABLE `Sy_Forumlog`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `uid` varchar(240) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `time` date NOT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for Sy_IPerror
-- ----------------------------
DROP TABLE IF EXISTS `Sy_IPerror`;
CREATE TABLE `Sy_IPerror`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `ipAdd` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT 'ip归属地',
  `Time` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '时间',
  `State` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '拉黑ip',
  `text` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '备注',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for Sy_Signinlog
-- ----------------------------
DROP TABLE IF EXISTS `Sy_Signinlog`;
CREATE TABLE `Sy_Signinlog`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `uid` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `time` datetime NOT NULL,
  `continuous` int(5) NOT NULL COMMENT '连续签到',
  `assets` int(5) NOT NULL COMMENT '积分',
  `exp` int(5) NOT NULL COMMENT '经验',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 41 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for Sy_Tippinglog
-- ----------------------------
DROP TABLE IF EXISTS `Sy_Tippinglog`;
CREATE TABLE `Sy_Tippinglog`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `uid` varchar(240) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `time` date NOT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 3 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for Sy_banip
-- ----------------------------
DROP TABLE IF EXISTS `Sy_banip`;
CREATE TABLE `Sy_banip`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `ipAdd` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT 'ip归属地',
  `Time` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '时间',
  `State` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '拉黑ip',
  `text` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '备注',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for Sy_functions
-- ----------------------------
DROP TABLE IF EXISTS `Sy_functions`;
CREATE TABLE `Sy_functions`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `Vipdiscount` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `Vipprivilege` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL,
  `Vippackage` int(11) NULL DEFAULT NULL,
  `Vippackagetitle` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `Vippackagetext` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL,
  `Task1` int(11) NULL DEFAULT NULL,
  `Taskexp1` int(11) NULL DEFAULT NULL,
  `Taskasset1` int(11) NULL DEFAULT NULL,
  `Task2` int(11) NULL DEFAULT NULL,
  `Taskexp2` int(11) NULL DEFAULT NULL,
  `Taskasset2` int(11) NULL DEFAULT NULL,
  `Tasklimit2` int(11) NULL DEFAULT NULL,
  `Task3` int(11) NULL DEFAULT NULL,
  `Taskexp3` int(11) NULL DEFAULT NULL,
  `Taskasset3` int(11) NULL DEFAULT NULL,
  `Tasklimit3` int(11) NULL DEFAULT NULL,
  `Task4` int(11) NULL DEFAULT NULL,
  `Taskexp4` int(11) NULL DEFAULT NULL,
  `Taskasset4` int(11) NULL DEFAULT NULL,
  `Tasklimit4` int(11) NULL DEFAULT NULL,
  `Task5` int(11) NULL DEFAULT NULL,
  `Taskexp5` int(11) NULL DEFAULT NULL,
  `Taskasset5` int(11) NULL DEFAULT NULL,
  `Tasklimit5` int(11) NULL DEFAULT NULL,
  `Task6` int(11) NULL DEFAULT NULL,
  `Taskexp6` int(11) NULL DEFAULT NULL,
  `Taskasset6` int(11) NULL DEFAULT NULL,
  `Tasklimit6` int(11) NULL DEFAULT NULL,
  `Taskasset7` int(11) NULL DEFAULT NULL,
  `Taskexp7` int(11) NULL DEFAULT NULL,
  `Taskasset8` int(11) NULL DEFAULT NULL,
  `Taskexp8` int(11) NULL DEFAULT NULL,
  `Tasklimit9` int(11) NULL DEFAULT NULL,
  `Taskexp9` int(11) NULL DEFAULT NULL,
  `Taskasset9` int(11) NULL DEFAULT NULL,
  `Signinexp1` int(11) NULL DEFAULT NULL,
  `Signinasset1` int(11) NULL DEFAULT NULL,
  `Signinexp2` int(11) NULL DEFAULT NULL,
  `Signinasset2` int(11) NULL DEFAULT NULL,
  `Signinexp3` int(11) NULL DEFAULT NULL,
  `Signinasset3` int(11) NULL DEFAULT NULL,
  `Signinexp4` int(11) NULL DEFAULT NULL,
  `Signinasset4` int(11) NULL DEFAULT NULL,
  `Signinexp5` int(11) NULL DEFAULT NULL,
  `Signinasset5` int(11) NULL DEFAULT NULL,
  `Signinexp6` int(11) NULL DEFAULT NULL,
  `Signinasset6` int(11) NULL DEFAULT NULL,
  `Signinexp7` int(11) NULL DEFAULT NULL,
  `Signinasset7` int(11) NULL DEFAULT NULL,
  `Task9` int(11) NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 2 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for Sy_icon
-- ----------------------------
DROP TABLE IF EXISTS `Sy_icon`;
CREATE TABLE `Sy_icon`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `lgof` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `link` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 7 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for Sy_ip
-- ----------------------------
DROP TABLE IF EXISTS `Sy_ip`;
CREATE TABLE `Sy_ip`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `ipAdd` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '\r\nip归属地',
  `Time` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '访问时间',
  `State` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT 'ip',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 22 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for Sy_login
-- ----------------------------
DROP TABLE IF EXISTS `Sy_login`;
CREATE TABLE `Sy_login`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '登录用户名',
  `pw` char(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '登录密码',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 2 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for Sy_pages
-- ----------------------------
DROP TABLE IF EXISTS `Sy_pages`;
CREATE TABLE `Sy_pages`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `Announcement` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL,
  `Displaytime` int(64) NULL DEFAULT NULL,
  `Searchtext` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `Carouselswitch` int(11) NULL DEFAULT NULL,
  `Iconswitch` int(11) NULL DEFAULT NULL,
  `Noticeswitch` int(11) NULL DEFAULT NULL,
  `Notice` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL,
  `Postswitch` int(11) NULL DEFAULT NULL,
  `Bannerswitch` int(11) NULL DEFAULT NULL,
  `Bannernumber` int(11) NULL DEFAULT NULL,
  `Bannerimg1` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `Bannerurl1` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `Bannerimg2` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `Bannerurl2` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `Bannerimg3` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `Bannerurl3` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `Bannerimg4` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `Bannerurl4` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `Bannerimg5` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `Bannerurl5` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `Bannerimg6` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `Bannerurl6` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `Admin` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `Gallery` int(11) NULL DEFAULT NULL,
  `Code` int(11) NULL DEFAULT NULL,
  `Hyperlinks` int(11) NULL DEFAULT NULL,
  `Comments` int(11) NULL DEFAULT NULL,
  `Image` int(11) NULL DEFAULT NULL,
  `Video` int(11) NULL DEFAULT NULL,
  `Topic` int(11) NULL DEFAULT NULL,
  `Shop` int(11) NULL DEFAULT NULL,
  `Viptext` int(11) NULL DEFAULT NULL,
  `Music` int(11) NULL DEFAULT NULL,
  `Musicimg1` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `Musicimg2` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `Musicimg3` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `Usernumber` int(11) NULL DEFAULT NULL,
  `Circlestyle` int(11) NULL DEFAULT NULL,
  `Dynamicimg` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `homeMode` int(11) NULL DEFAULT 1,
  `topStyle` int(11) NULL DEFAULT 2,
  `actStyle` int(11) NULL DEFAULT 3,
  `swiperStyle` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT 'true',
  `swiperOf` int(11) NULL DEFAULT 1,
  `iconOf` int(11) NULL DEFAULT 1,
  `kuaijie` int(11) NULL DEFAULT 0,
  `radiusBoxStyle` int(11) NULL DEFAULT 2,
  `swiperStyle2` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT 'true',
  `radiusStyle` int(11) NULL DEFAULT 2,
  `fatherTitle` int(11) NULL DEFAULT 1,
  `swiperType` int(11) NULL DEFAULT 2,
  `noticeOf` int(11) NULL DEFAULT 1,
  `circleOf` int(11) NULL DEFAULT 1,
  `recommendOf` int(11) NULL DEFAULT 1,
  `Hometop` int(11) NOT NULL DEFAULT 0,
  `Findtop` int(11) NOT NULL DEFAULT 1,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 2 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for Sy_popups
-- ----------------------------
DROP TABLE IF EXISTS `Sy_popups`;
CREATE TABLE `Sy_popups`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `Postpopup` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL,
  `Shoppopup` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL,
  `Taskpopup` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL,
  `Signpopup` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL,
  `Alipaypopup` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL,
  `Wechatpopup` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL,
  `Camipopup` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL,
  `Yipaypopup` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL,
  `Loginpopup` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL,
  `Registpopup` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL,
  `Forgetpopup` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL,
  `lvtext` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL,
  `smtext` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL,
  `yqtext` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL,
  `settext` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL,
  `hometext` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL,
  `home2text` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL,
  `homecsstext` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 2 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for Sy_search_limit
-- ----------------------------
DROP TABLE IF EXISTS `Sy_search_limit`;
CREATE TABLE `Sy_search_limit`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `search_limit_switch` int(1) NULL DEFAULT 0,
  `daily_search_limit` int(11) NULL DEFAULT 10,
  `reset_time` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `created` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 2 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for Sy_set
-- ----------------------------
DROP TABLE IF EXISTS `Sy_set`;
CREATE TABLE `Sy_set`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `Waiterurl` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '客服链接',
  `Groupurl` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '群聊链接',
  `Auditurl` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '审核员链接',
  `Share` tinyint(1) NOT NULL DEFAULT 0 COMMENT '分享开关 1开 0关',
  `Tipping` tinyint(1) NOT NULL DEFAULT 0 COMMENT '打赏开关 1开 0关',
  `Payswith` tinyint(1) NOT NULL DEFAULT 0 COMMENT '充值开关 1开 0关',
  `Alipay` tinyint(1) NOT NULL DEFAULT 0 COMMENT '支付宝支付 1开 0关',
  `WePay` tinyint(1) NOT NULL DEFAULT 0 COMMENT '微信支付 1开 0关',
  `Cami` tinyint(1) NOT NULL DEFAULT 0 COMMENT '卡密兑换 1开 0关',
  `Yipay` tinyint(1) NOT NULL DEFAULT 0 COMMENT '易支付 1开 0关',
  `Qlogin` tinyint(1) NOT NULL DEFAULT 0 COMMENT 'QQ登录 1开 0关',
  `wxlogin` tinyint(1) NOT NULL DEFAULT 0 COMMENT '微信登录 1开 0关',
  `wblogin` tinyint(1) NOT NULL DEFAULT 0 COMMENT '微博登录 1开 0关',
  `Tippingstyle` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '2' COMMENT '打赏样式',
  `Assetname` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '积分' COMMENT '货币名称',
  `Withdrawals` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '0' COMMENT '提现权限',
  `Premium` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '0' COMMENT '提现手续费',
  `Qgroup` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT 'contributor' COMMENT 'QQ用户组',
  `Threshold` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '0' COMMENT '提现门槛',
  `Dummy` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '0' COMMENT '用户冲假数量',
  `Viewspw` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '123456' COMMENT '刷浏览量密码',
  `h5of` int(11) NOT NULL DEFAULT 0,
  `Lvof` int(11) NOT NULL DEFAULT 0,
  `wzof` tinyint(1) NOT NULL DEFAULT 1,
  `tzof` tinyint(1) NOT NULL DEFAULT 1,
  `homeStyle` int(11) NOT NULL DEFAULT 2,
  `shopof` int(11) NOT NULL DEFAULT 1,
  `modOrder` int(11) NOT NULL DEFAULT 1,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 2 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '系统设置表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for Sy_update
-- ----------------------------
DROP TABLE IF EXISTS `Sy_update`;
CREATE TABLE `Sy_update`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `version` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `versionIntro` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `versionUrl` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `versionCode` int(11) NULL DEFAULT NULL,
  `force` int(11) NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for Sy_user_search_records
-- ----------------------------
DROP TABLE IF EXISTS `Sy_user_search_records`;
CREATE TABLE `Sy_user_search_records`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `uid` int(11) NOT NULL,
  `date` date NOT NULL,
  `search_count` int(11) NULL DEFAULT 0,
  `created` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `uid`(`uid`, `date`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 12 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for Sy_warning
-- ----------------------------
DROP TABLE IF EXISTS `Sy_warning`;
CREATE TABLE `Sy_warning`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `ip` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT 'ip地址',
  `gsd` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '归属地',
  `time` varchar(80) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '时间',
  `file` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '路径',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for typecho_ads
-- ----------------------------
DROP TABLE IF EXISTS `typecho_ads`;
CREATE TABLE `typecho_ads`  (
  `aid` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT '' COMMENT '广告名称',
  `type` int(11) NULL DEFAULT 0 COMMENT '广告类型（0推流，1横幅，2启动图，3轮播图）',
  `img` varchar(500) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '广告缩略图',
  `close` int(10) NULL DEFAULT 0 COMMENT '0代表永久，其它代表结束时间',
  `created` int(10) UNSIGNED NULL DEFAULT 0 COMMENT '创建时间',
  `price` int(11) UNSIGNED NULL DEFAULT 0 COMMENT '购买价格',
  `intro` varchar(500) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT '' COMMENT '广告简介',
  `urltype` int(11) NULL DEFAULT 0 COMMENT '0为APP内部打开，1为跳出APP',
  `url` text CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '跳转Url',
  `uid` int(11) NULL DEFAULT -1 COMMENT '发布人',
  `status` int(2) NULL DEFAULT 0 COMMENT '0审核中，1已公开，2已到期',
  PRIMARY KEY (`aid`) USING BTREE
) ENGINE = MyISAM AUTO_INCREMENT = 1 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '广告表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for typecho_apiconfig
-- ----------------------------
DROP TABLE IF EXISTS `typecho_apiconfig`;
CREATE TABLE `typecho_apiconfig`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `webinfoTitle` varchar(500) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '网站名称',
  `webinfoUrl` varchar(500) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '网站URL',
  `webinfoUploadUrl` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT 'http://127.0.0.1:8081/' COMMENT '本地图片访问路径',
  `webinfoAvatar` varchar(500) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT 'https://cravatar.cn/wp-content/themes/cravatar/assets/img/img1.png#' COMMENT '头像源',
  `pexelsKey` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '图库key',
  `scale` int(11) NOT NULL DEFAULT 100 COMMENT '一元能买多少积分',
  `clock` int(11) NOT NULL DEFAULT 0 COMMENT '签到最多多少积分',
  `vipPrice` int(11) NOT NULL DEFAULT 200 COMMENT 'VIP一天价格',
  `vipDay` int(11) NOT NULL DEFAULT 300 COMMENT '多少天VIP等于永久',
  `vipDiscount` varchar(11) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '0.1' COMMENT 'VIP折扣',
  `isEmail` int(2) NOT NULL DEFAULT 1 COMMENT '邮箱开关（0完全关闭邮箱，1只开启邮箱注册，2邮箱注册和操作通知）',
  `isInvite` int(11) NOT NULL DEFAULT 0 COMMENT '注册是否验证邀请码（默认关闭）',
  `cosAccessKey` varchar(300) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '',
  `cosSecretKey` varchar(300) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '',
  `cosBucket` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '',
  `cosBucketName` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '',
  `cosPath` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT '',
  `cosPrefix` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '',
  `aliyunEndpoint` varchar(500) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '',
  `aliyunAccessKeyId` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '',
  `aliyunAccessKeySecret` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '',
  `aliyunAucketName` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '',
  `aliyunUrlPrefix` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '',
  `aliyunFilePrefix` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '',
  `ftpHost` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '',
  `ftpPort` int(11) NOT NULL DEFAULT 21,
  `ftpUsername` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '',
  `ftpPassword` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '',
  `ftpBasePath` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '',
  `alipayAppId` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '',
  `alipayPrivateKey` text CHARACTER SET utf8 COLLATE utf8_general_ci NULL,
  `alipayPublicKey` text CHARACTER SET utf8 COLLATE utf8_general_ci NULL,
  `alipayNotifyUrl` varchar(500) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '',
  `appletsAppid` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '',
  `appletsSecret` text CHARACTER SET utf8 COLLATE utf8_general_ci NULL,
  `wxpayAppId` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '',
  `wxpayMchId` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '',
  `wxpayKey` text CHARACTER SET utf8 COLLATE utf8_general_ci NULL,
  `wxpayNotifyUrl` varchar(500) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT '',
  `auditlevel` int(2) NULL DEFAULT 1,
  `forbidden` text CHARACTER SET utf8 COLLATE utf8_general_ci NULL,
  `qqAppletsAppid` varchar(500) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `qqAppletsSecret` varchar(500) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `wxAppId` varchar(500) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `wxAppSecret` varchar(500) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `fields` varchar(500) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT 'able',
  `pushAdsPrice` int(11) NOT NULL DEFAULT 100 COMMENT '推流广告价格(积分/天)',
  `pushAdsNum` int(11) NOT NULL DEFAULT 10 COMMENT '推流广告数量',
  `bannerAdsPrice` int(11) NOT NULL DEFAULT 100 COMMENT '横幅广告价格(积分/天)',
  `bannerAdsNum` int(11) NOT NULL DEFAULT 5 COMMENT '横幅广告数量',
  `startAdsPrice` int(11) NOT NULL DEFAULT 100 COMMENT '启动图广告价格(积分/天)',
  `startAdsNum` int(11) NOT NULL DEFAULT 1 COMMENT '启动图广告数量',
  `epayUrl` varchar(500) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT '' COMMENT '易支付接口地址',
  `epayPid` int(11) NULL DEFAULT NULL COMMENT '易支付商户ID',
  `epayKey` varchar(300) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT '' COMMENT '易支付商户密钥',
  `epayNotifyUrl` varchar(500) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT '' COMMENT '易支付回调地址',
  `mchSerialNo` text CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '微信支付商户证书序列号',
  `mchApiV3Key` text CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '微信支付API3私钥',
  `cloudUid` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT '' COMMENT '云控UID',
  `cloudUrl` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT '' COMMENT '云控URL',
  `pushAppId` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT '' COMMENT 'pushAppId',
  `pushAppKey` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT '' COMMENT 'pushAppKey',
  `pushMasterSecret` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT '' COMMENT 'pushMasterSecret',
  `isPush` int(2) NULL DEFAULT 0 COMMENT '是否开启消息通知',
  `disableCode` int(2) NULL DEFAULT 0 COMMENT '是否禁用代码',
  `allowDelete` int(2) NULL DEFAULT 0 COMMENT '是否允许用户删除文章或评论',
  `contentAuditlevel` int(2) NULL DEFAULT 2 COMMENT '内容审核模式',
  `uploadLevel` int(2) NULL DEFAULT 0 COMMENT '上传限制等级（0只允许图片，1关闭上传接口，2只允许图片视频，3允许所有类型文件）',
  `clockExp` int(11) NULL DEFAULT 0 COMMENT '签到经验',
  `reviewExp` int(11) NULL DEFAULT 0 COMMENT '每日前三次评论经验',
  `postExp` int(11) NULL DEFAULT 0 COMMENT '每日前三次发布内容经验（文章，动态，帖子）',
  `violationExp` int(11) NULL DEFAULT 0 COMMENT '违规扣除经验',
  `deleteExp` int(11) NULL DEFAULT 0 COMMENT '删除扣除经验（文章，评论，动态，帖子）',
  `spaceMinExp` int(11) NULL DEFAULT 20 COMMENT '发布动态要求最低经验值',
  `chatMinExp` int(11) NULL DEFAULT 20 COMMENT '聊天要求最低经验值',
  `qiniuDomain` varchar(400) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT '' COMMENT '七牛云访问域名',
  `qiniuAccessKey` varchar(400) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT '' COMMENT '七牛云公钥',
  `qiniuSecretKey` varchar(400) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT '' COMMENT '七牛云私钥',
  `qiniuBucketName` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT '' COMMENT '七牛云存储桶名称',
  `codeAccessKeyId` varchar(400) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT '' COMMENT '阿里云短信AccessKeyId',
  `codeAccessKeySecret` varchar(400) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT '' COMMENT '阿里云短信AccessKeySecret',
  `codeEndpoint` varchar(400) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT 'dysmsapi.aliyuncs.com' COMMENT '阿里云短信请求地址',
  `codeTemplate` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT '' COMMENT '阿里云短信请求地址',
  `codeSignName` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT 'StarPro' COMMENT '阿里云短信签名',
  `isPhone` int(2) NULL DEFAULT 0 COMMENT '是否开启手机号支持',
  `silenceTime` int(11) NULL DEFAULT 600 COMMENT '疑似攻击自动封禁时间(s)',
  `interceptTime` int(11) NULL DEFAULT 3600 COMMENT '多次触发违规自动封禁时间(s)',
  `isLogin` int(2) NULL DEFAULT 0 COMMENT '开启全局登录',
  `postMax` int(11) NULL DEFAULT 999 COMMENT '每日最大发布',
  `forumAudit` int(11) NULL DEFAULT 1 COMMENT '帖子及帖子评论是否需要审核',
  `spaceAudit` int(11) NULL DEFAULT 0 COMMENT '动态是否需要审核',
  `uploadType` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT 'local' COMMENT '上传类型',
  `banRobots` int(2) NULL DEFAULT 0 COMMENT '是否开启机器人严格限制模式',
  `adsGiftNum` int(11) NULL DEFAULT 10 COMMENT '每日广告奖励次数',
  `adsGiftAward` int(11) NULL DEFAULT 5 COMMENT '每日广告奖励额',
  `verifyLevel` int(2) NULL DEFAULT 1 COMMENT '图片验证等级（0关闭，1发信验证，2全局验证）',
  `rebateLevel` int(2) NULL DEFAULT 0 COMMENT '邀请返利等级（0关闭，1固定奖励，2分成奖励，3全局奖励）',
  `rebateNum` int(11) NULL DEFAULT 10 COMMENT '固定奖励（多少平台货币）',
  `rebateProportion` int(11) NULL DEFAULT 5 COMMENT '翻译比例（百分之多少）',
  `uploadPicMax` int(11) NULL DEFAULT 5 COMMENT '图片最大上传大小',
  `uploadMediaMax` int(11) NULL DEFAULT 50 COMMENT '媒体最大上传大小',
  `uploadFilesMax` int(11) NULL DEFAULT 20 COMMENT '其他文件最大上传大小',
  `forumReplyAudit` int(2) NULL DEFAULT 0 COMMENT '帖子评论是否需要审核',
  `banIP` text CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '封禁IP列表',
  `identifyiLv` int(2) NULL DEFAULT 0 COMMENT '实名认证等级，0关闭，1个人，2企业，3全部',
  `identifyiIdcardHost` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT 'https://idcert.market.alicloudapi.com' COMMENT '个人认证接口地址',
  `identifyiIdcardPath` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT '/idcard' COMMENT '个人认证接口路径',
  `identifyiIdcardAppcode` text CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '个人认证APPCode',
  `identifyiCompanyHost` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT 'https://qy4ys.market.alicloudapi.com' COMMENT '企业认证接口地址',
  `identifyiCompanyPath` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT '/qysys/dmp/api/jinrun.company.company.elements4' COMMENT '企业认证接口路径',
  `identifyiCompanyAppcode` text CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '企业认证APPCode',
  `clockPoints` int(11) NULL DEFAULT 0 COMMENT '签到奖励积分数量',
  `identifylvPost` int(2) NULL DEFAULT 0 COMMENT '蓝V认证后才允许发布内容',
  `identifysmPost` int(2) NULL DEFAULT 0 COMMENT '实名认证后才允许发布内容',
  `cmsSecretId` text CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '腾讯云内容安全SecretId',
  `cmsSecretKey` text CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '腾讯云内容安全SecretKey',
  `cmsRegion` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT 'ap-shanghai' COMMENT '腾讯云内容安全接口地域',
  `cmsSwitch` int(2) NULL DEFAULT 0 COMMENT '腾讯云内容安全开关（0关闭，1文本，2图片，3全部）',
  `localPath` text CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '本地存储地址',
  `adsVideoType` int(2) NULL DEFAULT 1 COMMENT '激励广告模式（0前端回调，1服务端回调）',
  `adsSecuritykey` text CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '激励广告安全码',
  `smsType` int(2) NULL DEFAULT 0 COMMENT '短信发信类型',
  `smsbaoUsername` text CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '短信宝Username',
  `smsbaoApikey` text CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '短信宝Apikey',
  `smsbaoTemplate` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT '' COMMENT '短信宝Template',
  `identifyFee` double NULL DEFAULT 0,
  `inviteCodePrice` int(11) NULL DEFAULT 10 COMMENT '邀请码购买价格（元）',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = MyISAM AUTO_INCREMENT = 2 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = 'api配置信息表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for typecho_app
-- ----------------------------
DROP TABLE IF EXISTS `typecho_app`;
CREATE TABLE `typecho_app`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `key` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '链接密钥',
  `name` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '应用名称',
  `type` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT 'app' COMMENT '应用类型（web或App）',
  `logo` varchar(500) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT 'logo图标地址',
  `keywords` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT 'web专属，SEO关键词',
  `description` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '应用简介',
  `announcement` varchar(400) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '弹窗公告（支持html）',
  `mail` varchar(400) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '邮箱地址（用于通知和显示）',
  `website` varchar(400) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '网址（非Api地址）',
  `currencyName` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '货币名称',
  `version` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT 'v1.0.0 beta' COMMENT 'app专属，版本号',
  `versionCode` int(11) NULL DEFAULT 10 COMMENT 'app专属，版本码',
  `versionIntro` varchar(400) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '版本简介',
  `androidUrl` varchar(400) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '安卓下载地址',
  `iosUrl` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT 'ios下载地址',
  `field1` varchar(400) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '预留字段1',
  `field2` varchar(400) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '预留字段2',
  `adpid` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '广告联盟ID',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = MyISAM AUTO_INCREMENT = 2 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '应用表（web应用和APP应用）' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for typecho_chat
-- ----------------------------
DROP TABLE IF EXISTS `typecho_chat`;
CREATE TABLE `typecho_chat`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `chatid` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '聊天室id（加密值）',
  `uid` int(11) NULL DEFAULT 0 COMMENT '创建者',
  `toid` int(11) NULL DEFAULT 0 COMMENT '也是创建者（和上一个字段共同判断私聊）',
  `created` int(10) UNSIGNED NULL DEFAULT 0 COMMENT '创建时间',
  `lastTime` int(10) UNSIGNED NULL DEFAULT 0 COMMENT '最后聊天时间',
  `type` int(2) UNSIGNED NULL DEFAULT 0 COMMENT '0是私聊，1是群聊',
  `name` varchar(400) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '聊天室名称（群聊）',
  `pic` varchar(400) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '图片地址（群聊）',
  `ban` int(11) UNSIGNED NULL DEFAULT 0 COMMENT '屏蔽和全体禁言，存操作人id',
  `myUnRead` int(11) UNSIGNED NULL DEFAULT 0 COMMENT '我未读（只对私聊生效）',
  `otherUnRead` int(11) UNSIGNED NULL DEFAULT 0 COMMENT '对方未读（只对私聊生效）',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = MyISAM AUTO_INCREMENT = 5 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '聊天室表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for typecho_chat_msg
-- ----------------------------
DROP TABLE IF EXISTS `typecho_chat_msg`;
CREATE TABLE `typecho_chat_msg`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `uid` int(11) NULL DEFAULT 0 COMMENT '发送人',
  `cid` int(11) NULL DEFAULT 0 COMMENT '聊天室',
  `text` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '消息内容',
  `created` int(10) UNSIGNED NULL DEFAULT 0 COMMENT '发送时间',
  `type` int(2) UNSIGNED NULL DEFAULT 0 COMMENT '0文字消息，1图片消息，3视频消息，4系统提示',
  `url` varchar(400) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '为链接时的url',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = MyISAM AUTO_INCREMENT = 393 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '聊天消息' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for typecho_chatmsg
-- ----------------------------
DROP TABLE IF EXISTS `typecho_chatmsg`;
CREATE TABLE `typecho_chatmsg`  (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '消息ID',
  `cid` int(11) NOT NULL COMMENT '聊天室ID',
  `uid` int(11) NOT NULL COMMENT '发送用户ID',
  `text` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '消息内容',
  `url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '图片/文件URL',
  `type` int(1) NOT NULL DEFAULT 0 COMMENT '消息类型：0文本，1图片，2文件，3视频，4系统消息',
  `created` int(11) NOT NULL COMMENT '创建时间戳',
  `blocked` tinyint(1) NULL DEFAULT 0 COMMENT '是否被屏蔽',
  `blockedMsg` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '屏蔽原因',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_cid`(`cid`) USING BTREE,
  INDEX `idx_uid`(`uid`) USING BTREE,
  INDEX `idx_created`(`created`) USING BTREE,
  INDEX `idx_cid_created`(`cid`, `created`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '聊天消息表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for typecho_comments
-- ----------------------------
DROP TABLE IF EXISTS `typecho_comments`;
CREATE TABLE `typecho_comments`  (
  `coid` int(10) UNSIGNED NOT NULL AUTO_INCREMENT,
  `cid` int(10) UNSIGNED NULL DEFAULT 0,
  `created` int(10) UNSIGNED NULL DEFAULT 0,
  `author` varchar(200) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `authorId` int(10) UNSIGNED NULL DEFAULT 0,
  `ownerId` int(10) UNSIGNED NULL DEFAULT 0,
  `mail` varchar(200) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `url` varchar(200) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `ip` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `agent` varchar(520) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `text` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL,
  `type` varchar(16) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT 'comment',
  `status` varchar(16) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT 'approved',
  `parent` int(10) UNSIGNED NULL DEFAULT 0,
  `likes` int(11) NULL DEFAULT 0,
  `pic` text CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '图片',
  PRIMARY KEY (`coid`) USING BTREE,
  INDEX `cid`(`cid`) USING BTREE,
  INDEX `created`(`created`) USING BTREE
) ENGINE = MyISAM AUTO_INCREMENT = 4 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for typecho_company
-- ----------------------------
DROP TABLE IF EXISTS `typecho_company`;
CREATE TABLE `typecho_company`  (
  `uid` int(11) NOT NULL COMMENT '用户ID',
  `regno` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '企业工商注册号/统一信用代码',
  `name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '企业法人姓名',
  `entname` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '企业名称',
  `idcard` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '企业法人身份证号',
  `identifyStatus` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '认证状态：1成功、0失败',
  PRIMARY KEY (`uid`, `regno`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for typecho_consumer
-- ----------------------------
DROP TABLE IF EXISTS `typecho_consumer`;
CREATE TABLE `typecho_consumer`  (
  `uid` int(10) NOT NULL COMMENT '用户编码',
  `idCard` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '身份证号',
  `name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '姓名',
  `identifyStatus` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '审批状态，1为成功、0为失败',
  `mobile` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '手机号码',
  PRIMARY KEY (`uid`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for typecho_contents
-- ----------------------------
DROP TABLE IF EXISTS `typecho_contents`;
CREATE TABLE `typecho_contents`  (
  `cid` int(10) UNSIGNED NOT NULL AUTO_INCREMENT,
  `title` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL,
  `slug` varchar(200) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `created` int(10) UNSIGNED NULL DEFAULT 0,
  `modified` int(10) UNSIGNED NULL DEFAULT 0,
  `text` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL,
  `order` int(10) UNSIGNED NULL DEFAULT 0,
  `authorId` int(10) UNSIGNED NULL DEFAULT 0,
  `template` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `type` varchar(16) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT 'post',
  `status` varchar(16) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT 'publish',
  `password` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `commentsNum` int(10) UNSIGNED NULL DEFAULT 0,
  `allowComment` char(1) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT '0',
  `allowPing` char(1) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT '0',
  `allowFeed` char(1) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT '0',
  `parent` int(10) UNSIGNED NULL DEFAULT 0,
  `views` int(10) NULL DEFAULT 0,
  `likes` int(10) NULL DEFAULT 0,
  `isrecommend` int(2) NULL DEFAULT 0,
  `istop` int(2) NULL DEFAULT 0,
  `isswiper` int(2) NULL DEFAULT 0,
  `replyTime` int(10) NULL DEFAULT 0,
  PRIMARY KEY (`cid`) USING BTREE,
  UNIQUE INDEX `slug`(`slug`) USING BTREE,
  INDEX `created`(`created`) USING BTREE
) ENGINE = MyISAM AUTO_INCREMENT = 3 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for typecho_device_orders
-- ----------------------------
DROP TABLE IF EXISTS `typecho_device_orders`;
CREATE TABLE `typecho_device_orders`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `device_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `order_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `created_time` bigint(20) NOT NULL,
  `updated_time` bigint(20) NOT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `unique_device_order`(`device_id`, `order_id`) USING BTREE,
  INDEX `idx_device_id`(`device_id`) USING BTREE,
  INDEX `idx_order_id`(`order_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 9 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for typecho_emailtemplate
-- ----------------------------
DROP TABLE IF EXISTS `typecho_emailtemplate`;
CREATE TABLE `typecho_emailtemplate`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `verifyTemplate` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '验证码模板',
  `reviewTemplate` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '审核通知模板',
  `safetyTemplate` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '安全通知模板',
  `replyTemplate` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '评论&回复通知模板',
  `orderTemplate` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '订单通知模板',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = MyISAM AUTO_INCREMENT = 2 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '邮件模板' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for typecho_fan
-- ----------------------------
DROP TABLE IF EXISTS `typecho_fan`;
CREATE TABLE `typecho_fan`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `created` int(10) UNSIGNED NULL DEFAULT 0 COMMENT '关注时间',
  `uid` int(11) UNSIGNED NOT NULL DEFAULT 0 COMMENT '关注人',
  `touid` int(11) UNSIGNED NOT NULL DEFAULT 0 COMMENT '被关注人',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = MyISAM AUTO_INCREMENT = 4 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '关注表（全局内容）' ROW_FORMAT = Fixed;

-- ----------------------------
-- Table structure for typecho_fields
-- ----------------------------
DROP TABLE IF EXISTS `typecho_fields`;
CREATE TABLE `typecho_fields`  (
  `cid` int(10) UNSIGNED NOT NULL,
  `name` varchar(200) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `type` varchar(8) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT 'str',
  `str_value` text CHARACTER SET utf8 COLLATE utf8_general_ci NULL,
  `int_value` int(10) NULL DEFAULT 0,
  `float_value` float NULL DEFAULT 0,
  PRIMARY KEY (`cid`, `name`) USING BTREE,
  INDEX `int_value`(`int_value`) USING BTREE,
  INDEX `float_value`(`float_value`) USING BTREE
) ENGINE = MyISAM CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for typecho_forum
-- ----------------------------
DROP TABLE IF EXISTS `typecho_forum`;
CREATE TABLE `typecho_forum`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `title` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL,
  `section` int(11) NULL DEFAULT 0 COMMENT '所属版块',
  `typeid` int(11) NULL DEFAULT 0 COMMENT '所属板块子类',
  `created` int(10) UNSIGNED NULL DEFAULT 0 COMMENT '发布时间',
  `modified` int(10) UNSIGNED NULL DEFAULT 0 COMMENT '修改时间',
  `text` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL,
  `authorId` int(11) UNSIGNED NULL DEFAULT 0 COMMENT '作者uid',
  `status` int(2) NULL DEFAULT 0 COMMENT '帖子状态，0审核，1发布，2锁定',
  `commentsNum` int(11) UNSIGNED NULL DEFAULT 0 COMMENT '回帖数量',
  `views` int(11) NULL DEFAULT 0 COMMENT '阅读数量',
  `likes` int(11) NULL DEFAULT 0 COMMENT '点赞数量',
  `isTop` int(2) NULL DEFAULT 0 COMMENT '是否置顶，0普通，1当前版块置顶，2全局置顶',
  `isrecommend` int(2) NULL DEFAULT 0 COMMENT '是否加精&推荐',
  `isswiper` int(2) NULL DEFAULT 0 COMMENT '是否轮播',
  `replyTime` int(10) NULL DEFAULT 0 COMMENT '回复时间',
  `isMd` int(2) UNSIGNED NULL DEFAULT 1 COMMENT '是否为Markdown编辑器发布',
  `sid` int(2) UNSIGNED NULL DEFAULT 0 COMMENT '商品ID，用于付费阅读',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = MyISAM AUTO_INCREMENT = 14 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '圈子&论坛帖子' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for typecho_forum_comment
-- ----------------------------
DROP TABLE IF EXISTS `typecho_forum_comment`;
CREATE TABLE `typecho_forum_comment`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `uid` int(11) NULL DEFAULT 0 COMMENT '评论者',
  `created` int(10) UNSIGNED NULL DEFAULT 0 COMMENT '评论时间',
  `forumid` int(11) UNSIGNED NULL DEFAULT 0 COMMENT '所属帖子',
  `likes` int(11) UNSIGNED NULL DEFAULT 0 COMMENT '评论点赞',
  `text` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL,
  `parent` int(11) UNSIGNED NULL DEFAULT 0 COMMENT '上级评论',
  `section` int(11) UNSIGNED NULL DEFAULT 0 COMMENT '所属版块',
  `status` int(2) NULL DEFAULT 1 COMMENT '帖子评论状态，0为未审核，1为显示',
  `pic` text CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '图片',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = MyISAM AUTO_INCREMENT = 27 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '论坛评论' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for typecho_forum_moderator
-- ----------------------------
DROP TABLE IF EXISTS `typecho_forum_moderator`;
CREATE TABLE `typecho_forum_moderator`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `sectionId` int(11) NULL DEFAULT 0 COMMENT '版块id',
  `uid` int(11) NULL DEFAULT 0 COMMENT '用户id',
  `purview` int(11) NULL DEFAULT 0 COMMENT '权限等级依次（执行者，审核者，小版主，版主，大版主）',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = MyISAM AUTO_INCREMENT = 1 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '版主表（映射会员表）' ROW_FORMAT = Fixed;

-- ----------------------------
-- Table structure for typecho_forum_section
-- ----------------------------
DROP TABLE IF EXISTS `typecho_forum_section`;
CREATE TABLE `typecho_forum_section`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(300) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '版块名称',
  `pic` varchar(300) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '版块图片',
  `bg` varchar(300) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '板块背景图',
  `text` varchar(300) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '版块介绍',
  `type` varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT 'section' COMMENT 'sort为大类，section为版块',
  `restrict` int(11) NULL DEFAULT 0 COMMENT '发帖等级（根据权限表限制）',
  `parent` int(11) NULL DEFAULT 0 COMMENT '上级板块',
  `slug` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '缩略名',
  `order` int(11) NULL DEFAULT 0 COMMENT '排序',
  `isrecommend` int(2) NULL DEFAULT 0 COMMENT '是否推荐',
  `otal_views` bigint(20) NULL DEFAULT 0 COMMENT '总浏览量',
  `total_views` bigint(20) NULL DEFAULT 0 COMMENT '总浏览量',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = MyISAM AUTO_INCREMENT = 13 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '论坛版块' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for typecho_gpt
-- ----------------------------
DROP TABLE IF EXISTS `typecho_gpt`;
CREATE TABLE `typecho_gpt`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '模型名称',
  `source` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT 'Qwen' COMMENT '模型来源',
  `isVip` int(11) NULL DEFAULT 0 COMMENT '是否仅VIP可用',
  `price` int(11) NULL DEFAULT 0 COMMENT '每次请求消耗多少金币',
  `avatar` varchar(300) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '模型头像',
  `intro` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '模型简介',
  `created` int(10) UNSIGNED NULL DEFAULT 0 COMMENT '创建时间',
  `appId` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '大模型渠道appId',
  `apiKey` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '大模型渠道apiKey',
  `type` int(10) UNSIGNED NULL DEFAULT 0 COMMENT '应用类型 0是聊天大模型 1是AI应用',
  `prompt` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT 'prompt 仅AI应用需要设置',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = MyISAM AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = 'AI大模型表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for typecho_gpt_chat
-- ----------------------------
DROP TABLE IF EXISTS `typecho_gpt_chat`;
CREATE TABLE `typecho_gpt_chat`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `gptid` int(11) NULL DEFAULT 0 COMMENT '大模型id',
  `uid` int(11) NULL DEFAULT 0 COMMENT '用户ID',
  `sessionId` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '聊天sessionId',
  `created` int(10) UNSIGNED NULL DEFAULT 0 COMMENT '创建时间',
  `replyTime` int(10) NULL DEFAULT 0 COMMENT '最新回复时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = MyISAM AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = 'gpt聊天室' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for typecho_gpt_msg
-- ----------------------------
DROP TABLE IF EXISTS `typecho_gpt_msg`;
CREATE TABLE `typecho_gpt_msg`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `uid` int(11) NULL DEFAULT 0 COMMENT '发送人',
  `gptid` int(11) NULL DEFAULT 0 COMMENT '所选GPT',
  `text` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '消息内容',
  `created` int(10) UNSIGNED NULL DEFAULT 0 COMMENT '创建时间',
  `isAI` int(1) NULL DEFAULT 0 COMMENT '是否为AI回复',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = MyISAM AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = 'gpt聊天消息' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for typecho_inbox
-- ----------------------------
DROP TABLE IF EXISTS `typecho_inbox`;
CREATE TABLE `typecho_inbox`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `type` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '消息类型：system(系统消息)，comment(评论消息)，finance(财务消息)',
  `uid` int(11) NULL DEFAULT 0 COMMENT '消息发送人，0是平台',
  `text` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL,
  `touid` int(11) NOT NULL DEFAULT 0 COMMENT '消息接收人uid',
  `isread` int(2) NULL DEFAULT 0 COMMENT '是否已读，0已读，1未读',
  `value` int(11) NULL DEFAULT 0 COMMENT '消息指向内容的id，根据类型跳转',
  `created` int(10) UNSIGNED NULL DEFAULT 0 COMMENT '创建时间',
  `cid` int(11) NULL DEFAULT 0 COMMENT '次级消息内容ID',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = MyISAM AUTO_INCREMENT = 58 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '消息表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for typecho_invitation
-- ----------------------------
DROP TABLE IF EXISTS `typecho_invitation`;
CREATE TABLE `typecho_invitation`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `code` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '邀请码',
  `created` int(10) NULL DEFAULT 0 COMMENT '创建时间',
  `uid` int(11) NULL DEFAULT 0 COMMENT '创建者',
  `status` int(2) NULL DEFAULT 0 COMMENT '0未使用，1已使用',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = MyISAM AUTO_INCREMENT = 12 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '邀请码' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for typecho_metas
-- ----------------------------
DROP TABLE IF EXISTS `typecho_metas`;
CREATE TABLE `typecho_metas`  (
  `mid` int(10) UNSIGNED NOT NULL AUTO_INCREMENT,
  `name` varchar(200) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `slug` varchar(200) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `type` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `description` varchar(200) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `count` int(10) UNSIGNED NULL DEFAULT 0,
  `order` int(10) UNSIGNED NULL DEFAULT 0,
  `parent` int(10) UNSIGNED NULL DEFAULT 0,
  `imgurl` varchar(500) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `isrecommend` int(2) NULL DEFAULT 0,
  PRIMARY KEY (`mid`) USING BTREE,
  INDEX `slug`(`slug`) USING BTREE
) ENGINE = MyISAM AUTO_INCREMENT = 2 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for typecho_offline_message
-- ----------------------------
DROP TABLE IF EXISTS `typecho_offline_message`;
CREATE TABLE `typecho_offline_message`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `type` int(11) NOT NULL DEFAULT 0 COMMENT '消息类型 (0:文本, 1:图片)',
  `from_user_id` int(11) NOT NULL COMMENT '发送者用户ID',
  `to_user_id` int(11) NOT NULL COMMENT '接收者用户ID',
  `chat_id` int(11) NOT NULL COMMENT '聊天会话ID',
  `content` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '消息内容',
  `is_delivered` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否已发送 (0:未发送, 1:已发送)',
  `message_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '消息原始ID (用于WebSocket消息ID匹配)',
  `created` datetime NOT NULL COMMENT '创建时间',
  `delivered` datetime NULL DEFAULT NULL COMMENT '发送时间 (如果已发送)',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `ix_to_user_delivered`(`to_user_id`, `is_delivered`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '离线消息表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for typecho_paykey
-- ----------------------------
DROP TABLE IF EXISTS `typecho_paykey`;
CREATE TABLE `typecho_paykey`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `value` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT '' COMMENT '密钥',
  `price` int(11) NULL DEFAULT 0 COMMENT '等值积分',
  `status` int(2) NULL DEFAULT 0 COMMENT '0未使用，1已使用',
  `created` int(10) NULL DEFAULT 0 COMMENT '创建时间',
  `uid` int(11) NULL DEFAULT -1 COMMENT '使用用户',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = MyISAM AUTO_INCREMENT = 1 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '卡密充值相关' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for typecho_paylog
-- ----------------------------
DROP TABLE IF EXISTS `typecho_paylog`;
CREATE TABLE `typecho_paylog`  (
  `pid` int(11) NOT NULL AUTO_INCREMENT,
  `subject` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `total_amount` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `out_trade_no` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `trade_no` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `paytype` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT '' COMMENT '支付类型',
  `uid` int(11) NULL DEFAULT -1 COMMENT '充值人ID',
  `created` int(10) NULL DEFAULT NULL,
  `status` int(11) NULL DEFAULT 0 COMMENT '支付状态（0未支付，1已支付）',
  PRIMARY KEY (`pid`) USING BTREE
) ENGINE = MyISAM AUTO_INCREMENT = 191 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '支付渠道充值记录' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for typecho_relationships
-- ----------------------------
DROP TABLE IF EXISTS `typecho_relationships`;
CREATE TABLE `typecho_relationships`  (
  `cid` int(10) UNSIGNED NOT NULL,
  `mid` int(10) UNSIGNED NOT NULL,
  PRIMARY KEY (`cid`, `mid`) USING BTREE
) ENGINE = MyISAM CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Fixed;

-- ----------------------------
-- Table structure for typecho_shop
-- ----------------------------
DROP TABLE IF EXISTS `typecho_shop`;
CREATE TABLE `typecho_shop`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `title` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL,
  `imgurl` varchar(500) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '商品图片',
  `text` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL,
  `price` int(11) NULL DEFAULT 0 COMMENT '商品价格',
  `num` int(11) NULL DEFAULT 0 COMMENT '商品数量',
  `type` int(11) NULL DEFAULT 1 COMMENT '商品类型（实体，源码，工具，教程）',
  `value` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL,
  `cid` int(11) NULL DEFAULT -1 COMMENT '所属文章',
  `uid` int(11) NULL DEFAULT 0 COMMENT '发布人',
  `vipDiscount` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '0.1' COMMENT 'VIP折扣，权高于系统设置折扣',
  `created` int(10) NULL DEFAULT 0,
  `status` int(10) NULL DEFAULT 0,
  `sellNum` int(11) NULL DEFAULT 0,
  `isMd` int(2) UNSIGNED NULL DEFAULT 1 COMMENT '是否为Markdown编辑器发布',
  `sort` int(11) UNSIGNED NULL DEFAULT 0 COMMENT '商品大类',
  `subtype` int(11) UNSIGNED NULL DEFAULT 0 COMMENT '子类型',
  `isView` int(2) UNSIGNED NULL DEFAULT 1 COMMENT '是否可见',
  `integral` int(11) NULL DEFAULT 0 COMMENT '商品所需积分',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = MyISAM AUTO_INCREMENT = 3 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '商品表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for typecho_shoptype
-- ----------------------------
DROP TABLE IF EXISTS `typecho_shoptype`;
CREATE TABLE `typecho_shoptype`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `parent` int(11) NULL DEFAULT 0 COMMENT '上级分类',
  `name` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '分类名称',
  `pic` varchar(400) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '分类缩略图',
  `intro` varchar(400) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '分类简介',
  `orderKey` int(11) NULL DEFAULT 0 COMMENT '分类排序',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = MyISAM AUTO_INCREMENT = 11 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '商品分类表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for typecho_space
-- ----------------------------
DROP TABLE IF EXISTS `typecho_space`;
CREATE TABLE `typecho_space`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `uid` int(11) NULL DEFAULT 0 COMMENT '发布者',
  `created` int(10) UNSIGNED NULL DEFAULT 0 COMMENT '发布时间',
  `modified` int(10) UNSIGNED NULL DEFAULT 0 COMMENT '修改时间',
  `text` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '内容',
  `pic` text CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '图片或视频，自己拆分',
  `type` int(2) NULL DEFAULT NULL COMMENT '0普通动态，1转发和发布文章，2转发动态，3动态评论，4视频，5商品',
  `likes` int(10) NULL DEFAULT 0 COMMENT '喜欢动态的数量',
  `toid` int(10) NULL DEFAULT 0 COMMENT '文章id，动态id等',
  `status` int(2) UNSIGNED NULL DEFAULT 1 COMMENT '动态状态，0审核，1发布，2锁定',
  `onlyMe` int(2) UNSIGNED NULL DEFAULT 0 COMMENT '仅自己可见',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = MyISAM AUTO_INCREMENT = 2 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '个人动态表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for typecho_user_blacklist
-- ----------------------------
DROP TABLE IF EXISTS `typecho_user_blacklist`;
CREATE TABLE `typecho_user_blacklist`  (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '???ID',
  `uid` int(11) NOT NULL COMMENT '?????????ID',
  `black_uid` int(11) NOT NULL COMMENT '????????D',
  `created` int(11) NULL DEFAULT NULL COMMENT '??????',
  `expires` int(11) NULL DEFAULT NULL COMMENT '?????????VIP???????????',
  `status` int(1) NULL DEFAULT 1 COMMENT '????1???,0???)',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_uid`(`uid`) USING BTREE,
  INDEX `idx_black_uid`(`black_uid`) USING BTREE,
  INDEX `idx_uid_black_uid`(`uid`, `black_uid`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 37 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '?????????' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for typecho_userapi
-- ----------------------------
DROP TABLE IF EXISTS `typecho_userapi`;
CREATE TABLE `typecho_userapi`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `headImgUrl` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '头像，可能用不上',
  `openId` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '开放平台ID',
  `access_token` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '唯一值',
  `appLoginType` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '渠道类型',
  `uid` int(11) NULL DEFAULT 0 COMMENT '用户ID',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = MyISAM AUTO_INCREMENT = 1 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '社会化登陆' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for typecho_userlog
-- ----------------------------
DROP TABLE IF EXISTS `typecho_userlog`;
CREATE TABLE `typecho_userlog`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `uid` int(11) NOT NULL DEFAULT -1 COMMENT '用户id',
  `cid` int(11) NOT NULL DEFAULT 0,
  `type` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '类型',
  `num` int(11) NULL DEFAULT 0 COMMENT '数值，用于后期扩展',
  `created` int(10) NOT NULL DEFAULT 0 COMMENT '时间',
  `toid` int(11) NULL DEFAULT 0,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = MyISAM AUTO_INCREMENT = 18 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '用户日志（收藏，扩展等）' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for typecho_users
-- ----------------------------
DROP TABLE IF EXISTS `typecho_users`;
CREATE TABLE `typecho_users`  (
  `uid` int(10) UNSIGNED NOT NULL AUTO_INCREMENT,
  `name` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `password` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `mail` varchar(200) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `url` varchar(200) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT 'bm',
  `screenName` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `created` int(10) UNSIGNED NULL DEFAULT 0,
  `activated` int(10) UNSIGNED NULL DEFAULT 0,
  `logged` int(10) UNSIGNED NULL DEFAULT 0,
  `group` varchar(16) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT 'visitor',
  `authCode` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `introduce` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `assets` int(11) NULL DEFAULT 0,
  `address` text CHARACTER SET utf8 COLLATE utf8_general_ci NULL,
  `pay` text CHARACTER SET utf8 COLLATE utf8_general_ci NULL,
  `customize` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `vip` int(10) NULL DEFAULT 0,
  `experience` int(11) NULL DEFAULT 0,
  `avatar` text CHARACTER SET utf8 COLLATE utf8_general_ci NULL,
  `clientId` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `bantime` int(10) NULL DEFAULT 0,
  `posttime` int(10) NULL DEFAULT 0,
  `ip` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT '',
  `local` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT '',
  `phone` varchar(30) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT '',
  `userBg` varchar(400) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT '' COMMENT '用户主页背景图链接',
  `invitationCode` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT '' COMMENT '用户邀请码',
  `invitationUser` int(11) NULL DEFAULT 0 COMMENT '邀请用户',
  `points` int(11) NULL DEFAULT 0 COMMENT '用户积分',
  `qqbang` int(11) NOT NULL DEFAULT 0,
  `wxbang` int(11) NOT NULL DEFAULT 0,
  `wbbang` int(11) NOT NULL DEFAULT 0,
  `consumer` int(11) NOT NULL DEFAULT 0,
  `company` int(11) NOT NULL DEFAULT 0,
  `hideIp` int(1) NULL DEFAULT 0 COMMENT '是否隐藏IP (0: 不隐藏, 1: 隐藏)',
  PRIMARY KEY (`uid`) USING BTREE,
  UNIQUE INDEX `name`(`name`) USING BTREE,
  UNIQUE INDEX `mail`(`mail`) USING BTREE
) ENGINE = MyISAM AUTO_INCREMENT = 5 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for typecho_violation
-- ----------------------------
DROP TABLE IF EXISTS `typecho_violation`;
CREATE TABLE `typecho_violation`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `uid` int(11) NOT NULL DEFAULT 0 COMMENT '违规者uid',
  `type` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '处理类型（manager管理员操作，system系统自动）',
  `text` text CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '具体原因',
  `created` int(10) UNSIGNED NULL DEFAULT 0 COMMENT '违规时间',
  `handler` int(11) UNSIGNED NULL DEFAULT 0 COMMENT '处理人，0为系统自动，其它为真实用户',
  `value` int(11) NOT NULL DEFAULT 0 COMMENT '预留字段，用于指定范围禁言',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = MyISAM AUTO_INCREMENT = 2 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '违规记录表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for typecho_vips
-- ----------------------------
DROP TABLE IF EXISTS `typecho_vips`;
CREATE TABLE `typecho_vips`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `orderKey` int(11) NULL DEFAULT 0 COMMENT '排序，越大越靠前',
  `name` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '套餐名称（如：月付VIP）',
  `price` int(11) NULL DEFAULT 0 COMMENT '套餐价格，正整数',
  `day` int(11) NULL DEFAULT 0 COMMENT '获得VIP天数',
  `giftDay` int(11) NULL DEFAULT 0 COMMENT '额外奖励天数，为0则不奖励',
  `intro` varchar(400) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '套餐介绍',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = MyISAM AUTO_INCREMENT = 22 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = 'VIP类型表' ROW_FORMAT = Dynamic;

SET FOREIGN_KEY_CHECKS = 1;
